:root {
  --bs-primary: #1559b4;
  --bs-body-bg: #fff;
  --bs-body-color: var(--bs-gray-800);
}
[data-bs-theme="dark"] {
  --bs-primary: #1559b4;
  --bs-body-bg: #111827;
  --bs-body-color: var(--bs-gray-200);
  --bs-text-primary: #4a92e6;
}

body {
  color: var(--bs-body-color);
  background-color: var(--bs-body-bg);
  font-family: 'Inter', sans-serif;
  font-size: .875rem;
  overflow-x: hidden;
}

.df-header {
  margin: 0 auto;
}

.df-logo {
  text-decoration: none;
}

.df-logo__text {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--bs-body-color);
}

.df-logo__image {
  width: auto;
  height: 2rem;
}

.df-nav-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 3rem;
}

.df-nav-item {
  color: var(--bs-body-color) !important;
  text-decoration: none;
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.5rem;
}

.df-nav-item:hover {
  color: var(--bs-primary) !important;
}

.df-mobile-menu-btn {
  border: none;
  background: transparent;
}

.df-mobile-menu-btn:focus {
  box-shadow: none;
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

[data-bs-theme="dark"] .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.df-btn--outline-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: .5rem;
  padding: .5rem .75rem;
  border-radius: var(--bs-border-radius-pill);
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.25rem;
  border: 1px solid var(--bs-primary);
  cursor: pointer;
  color: var(--bs-primary);
  text-decoration: none;
  background-color: transparent;
  transition: .25s;
}

.df-btn--outline-primary:hover {
  color: var(--bs-white);
  background-color: var(--bs-primary);
}
[data-bs-theme="dark"] .df-btn--outline-primary {
  background-color: #4a92e61a;
  color: var(--bs-text-primary);
}
[data-bs-theme="dark"] .df-btn--outline-primary:hover {
  color: var(--bs-primary);
}
.icon {
  width: 1rem;
  height: 1rem;
}

/* df-intro */
.df-intro {
  position: relative;
  padding: 16rem 2rem;
  isolation: isolate;
}

.df-intro-main {
  position: absolute;
  left: 0;
  right: 0;
  top: 2.5rem;
  z-index: -10;
  display: flex;
  justify-content: center;
  overflow: hidden;
  filter: blur(64px);
}

.df-intro-main__image {
  aspect-ratio: 1108 / 632;
  width: 69.25rem;
  flex: none;
  background-image: linear-gradient(to right, #1559b4, #2563eb);
  opacity: .2;
}

.df-intro-content {
  margin: 0 auto;
  max-width: 56rem;
  text-align: center;
}

.df-intro-head {
  font-size: 3.5rem;
  font-weight: 600;
  letter-spacing: -.025rem;
  line-height: 1;
}

.df-intro-sub {
  font-size: 1rem;
  font-weight: 400;
}

.df-btn--primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: .5rem;
  padding: .75rem .95rem;
  border-radius: var(--bs-border-radius-pill);
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.25rem;
  border: 1px solid var(--bs-primary);
  cursor: pointer;
  color: var(--bs-white);
  text-decoration: none;
  background-color: var(--bs-primary);
  transition: .25s;
}
[data-bs-theme="dark"] .df-btn--primary {
  background-color: var(--bs-white);
  border-color: transparent;
  color: var(--bs-primary)
}
[data-bs-theme="dark"] .df-btn--primary:hover {
  background-color: var(--bs-white);
  border-color: transparent;
  color: var(--bs-primary)
}
.df-btn--primary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
  color: var(--bs-white);
}
.df-text-primary {
  color: var(--bs-primary);
  opacity: 1;
}
[data-bs-theme="dark"] .df-text-primary {
  color: var(--bs-text-primary);
}
.df-text-button{
  color:var(--bs-body-color);
  font-weight: 600;
  text-decoration: none;
}

.mt-8{
  margin-top: 8rem;
}

/* Services Section */
.df-services {
  position: relative;
  padding: 6rem 2rem;
  isolation: isolate;
}

.df-services-main {
  position: absolute;
  left: 0;
  right: 0;
  top: 2.5rem;
  z-index: -10;
  display: flex;
  justify-content: center;
  overflow: hidden;
  filter: blur(64px);
}

.df-services-main__image {
  aspect-ratio: 1108 / 632;
  width: 69.25rem;
  flex: none;
  background-image: linear-gradient(to right, #1559b4, #2563eb);
  opacity: .2;
  clip-path: polygon(73.6% 51.7%, 91.7% 11.8%, 100% 46.4%, 97.4% 82.2%, 92.5% 84.9%, 75.7% 64%, 55.3% 47.5%, 46.5% 49.4%, 45% 62.9%, 50.3% 87.2%, 21.3% 64.1%, 0.1% 100%, 5.4% 51.1%, 21.4% 63.9%, 58.9% 0.2%, 73.6% 51.7%);
}

.df-services-header {
  max-width: 56rem;
  margin: 0 auto;
  text-align: center;
}

.df-services-title {
  font-size: 3rem;
  font-weight: 600;
  color: var(--bs-body-color);
  margin-bottom: 1rem;
  letter-spacing: -.025rem;
  line-height: 1;
}

.df-services-subtitle {
  font-size: 1rem;
  color: var(--bs-body-color);
  opacity: 0.8;
  line-height: 1.6;
  font-weight: 400;
}

.df-platforms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-top: 4rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.df-platform-card {
  background: var(--bs-body-bg);
  border-radius: 1rem;
  padding: 2rem;
  border: 1px solid rgba(var(--bs-body-color-rgb), 0.1);
  transition: all 0.3s ease;
}

.df-platform-card:hover {
  border-color: rgba(var(--bs-primary-rgb), 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.df-platform-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.df-platform-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.df-platform-icon svg {
  width: 1.5rem;
  height: 1.5rem;
}

.df-platform-icon.facebook {
  color: #1877f2;
}

.df-platform-icon.instagram {
  color: #e4405f;
}

.df-platform-icon.tiktok {
  color: #000000;
}

.df-platform-icon.youtube {
  color: #ff0000;
}

.df-platform-icon.threads {
  color: #000000;
}

.df-platform-icon.twitter {
  color: #1da1f2;
}

.df-platform-info h3 {
  font-size: 1.25rem;
  font-weight: 500;
  color: var(--bs-body-color);
  margin: 0 0 0.25rem 0;
}

.df-platform-info p {
  font-size: 0.875rem;
  color: var(--bs-body-color);
  opacity: 0.7;
  margin: 0;
}

.df-services-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.df-service-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(var(--bs-body-color-rgb), 0.05);
}

.df-service-item:last-child {
  border-bottom: none;
}

.df-service-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--bs-primary);
  flex-shrink: 0;
}

.df-service-name {
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--bs-body-color);
}

/* About Section */
.df-about {
  position: relative;
  padding: 6rem 2rem;
  isolation: isolate;
}

.df-about-main {
  position: absolute;
  left: 0;
  right: 0;
  top: 2.5rem;
  z-index: -10;
  display: flex;
  justify-content: center;
  overflow: hidden;
  filter: blur(64px);
}

.df-about-main__image {
  aspect-ratio: 1108 / 632;
  width: 69.25rem;
  flex: none;
  background-image: linear-gradient(to right, #1559b4, #2563eb);
  opacity: .2;
  clip-path: polygon(73.6% 51.7%, 91.7% 11.8%, 100% 46.4%, 97.4% 82.2%, 92.5% 84.9%, 75.7% 64%, 55.3% 47.5%, 46.5% 49.4%, 45% 62.9%, 50.3% 87.2%, 21.3% 64.1%, 0.1% 100%, 5.4% 51.1%, 21.4% 63.9%, 58.9% 0.2%, 73.6% 51.7%);
}

.df-about-content {
  max-width: 56rem;
  margin: 0 auto;
  text-align: center;
}

.df-about-title {
  font-size: 3rem;
  font-weight: 600;
  color: var(--bs-body-color);
  margin-bottom: 1rem;
  letter-spacing: -.025rem;
  line-height: 1;
}

.df-about-subtitle {
  font-size: 1rem;
  color: var(--bs-body-color);
  opacity: 0.8;
  line-height: 1.6;
  font-weight: 400;
  margin-bottom: 3rem;
}

.df-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.df-feature-item {
  text-align: center;
  padding: 1.5rem;
}

.df-feature-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1rem;
  background: rgba(var(--bs-primary-rgb), 0.1);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--bs-primary);
}

.df-feature-icon svg {
  width: 2rem;
  height: 2rem;
}

.df-feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--bs-body-color);
  margin-bottom: 0.75rem;
}

.df-feature-desc {
  font-size: 0.875rem;
  color: var(--bs-body-color);
  opacity: 0.8;
  line-height: 1.6;
  margin: 0;
}

/* FAQ Section */
.df-faq {
  position: relative;
  padding: 6rem 2rem;
  isolation: isolate;
}

.df-faq-main {
  position: absolute;
  left: 0;
  right: 0;
  top: 2.5rem;
  z-index: -10;
  display: flex;
  justify-content: center;
  overflow: hidden;
  filter: blur(64px);
}

.df-faq-main__image {
  aspect-ratio: 1108 / 632;
  width: 69.25rem;
  flex: none;
  background-image: linear-gradient(to right, #1559b4, #2563eb);
  opacity: .2;
  clip-path: polygon(73.6% 51.7%, 91.7% 11.8%, 100% 46.4%, 97.4% 82.2%, 92.5% 84.9%, 75.7% 64%, 55.3% 47.5%, 46.5% 49.4%, 45% 62.9%, 50.3% 87.2%, 21.3% 64.1%, 0.1% 100%, 5.4% 51.1%, 21.4% 63.9%, 58.9% 0.2%, 73.6% 51.7%);
}

.df-faq-header {
  max-width: 56rem;
  margin: 0 auto;
  text-align: center;
  margin-bottom: 3rem;
}

.df-faq-title {
  font-size: 3rem;
  font-weight: 600;
  color: var(--bs-body-color);
  margin-bottom: 1rem;
  letter-spacing: -.025rem;
  line-height: 1;
}

.df-faq-subtitle {
  font-size: 1rem;
  color: var(--bs-body-color);
  opacity: 0.8;
  line-height: 1.6;
  font-weight: 400;
}

.df-faq-content {
  max-width: 48rem;
  margin: 0 auto;
}

.df-faq-item {
  border: 1px solid rgba(var(--bs-body-color-rgb), 0.1);
  border-radius: 1rem;
  margin-bottom: 1rem;
  overflow: hidden;
  background: var(--bs-body-bg);
  transition: all 0.3s ease;
}

.df-faq-item:hover {
  border-color: rgba(var(--bs-primary-rgb), 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.df-faq-question {
  width: 100%;
  padding: 1.5rem;
  background: none;
  border: none;
  text-align: left;
  font-size: 1rem;
  font-weight: 500;
  color: var(--bs-body-color);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  transition: all 0.2s ease;
}

.df-faq-question:hover {
  color: var(--bs-primary);
}

.df-faq-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.df-faq-item.active .df-faq-icon {
  transform: rotate(180deg);
}

.df-faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
}

.df-faq-item.active .df-faq-answer {
  max-height: 200px;
  padding: 0 1.5rem 1.5rem;
}

.df-faq-answer p {
  font-size: 0.875rem;
  color: var(--bs-body-color);
  opacity: 0.8;
  line-height: 1.6;
  margin: 0;
}

/* FAQ Section */
.df-faq {
  position: relative;
  padding: 6rem 2rem;
  isolation: isolate;
}

.df-faq-main {
  position: absolute;
  left: 0;
  right: 0;
  top: 2.5rem;
  z-index: -10;
  display: flex;
  justify-content: center;
  overflow: hidden;
  filter: blur(64px);
}

.df-faq-main__image {
  aspect-ratio: 1108 / 632;
  width: 69.25rem;
  flex: none;
  background-image: linear-gradient(to right, #1559b4, #2563eb);
  opacity: .2;
  clip-path: polygon(73.6% 51.7%, 91.7% 11.8%, 100% 46.4%, 97.4% 82.2%, 92.5% 84.9%, 75.7% 64%, 55.3% 47.5%, 46.5% 49.4%, 45% 62.9%, 50.3% 87.2%, 21.3% 64.1%, 0.1% 100%, 5.4% 51.1%, 21.4% 63.9%, 58.9% 0.2%, 73.6% 51.7%);
}

.df-faq-header {
  max-width: 56rem;
  margin: 0 auto;
  text-align: center;
  margin-bottom: 3rem;
}

.df-faq-title {
  font-size: 3rem;
  font-weight: 600;
  color: var(--bs-body-color);
  margin-bottom: 1rem;
  letter-spacing: -.025rem;
  line-height: 1;
}

.df-faq-subtitle {
  font-size: 1rem;
  color: var(--bs-body-color);
  opacity: 0.8;
  line-height: 1.6;
  font-weight: 400;
}

.df-faq-content {
  max-width: 48rem;
  margin: 0 auto;
}

.df-faq-item {
  border: 1px solid rgba(var(--bs-body-color-rgb), 0.1);
  border-radius: 1rem;
  margin-bottom: 1rem;
  overflow: hidden;
  background: var(--bs-body-bg);
  transition: all 0.3s ease;
}

.df-faq-item:hover {
  border-color: rgba(var(--bs-primary-rgb), 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.df-faq-question {
  width: 100%;
  padding: 1.5rem;
  background: none;
  border: none;
  text-align: left;
  font-size: 1rem;
  font-weight: 500;
  color: var(--bs-body-color);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  transition: all 0.2s ease;
}

.df-faq-question:hover {
  color: var(--bs-primary);
}

.df-faq-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.df-faq-item.active .df-faq-icon {
  transform: rotate(180deg);
}

.df-faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
}

.df-faq-item.active .df-faq-answer {
  max-height: 200px;
  padding: 0 1.5rem 1.5rem;
}

.df-faq-answer p {
  font-size: 0.875rem;
  color: var(--bs-body-color);
  opacity: 0.8;
  line-height: 1.6;
  margin: 0;
}

/* df-highlights */

/* Mobile Responsive Styles */
@media (max-width: 991.98px) {
  /* Header responsive */
  .df-nav-list {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
    text-align: center;
    margin-top: 1rem;
  }

  .df-header-cta {
    margin-top: 1rem;
    text-align: center;
  }

  .df-btn--outline-primary {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 768px) {
  /* Intro section responsive */
  .df-intro {
    padding: 6rem 1rem 4rem;
  }

  .df-intro-head {
    font-size: 2.5rem;
    line-height: 1.1;
  }

  .df-intro-sub {
    font-size: 0.9rem;
    margin-top: 1rem;
    padding: 0 1rem;
  }

  .df-intro-main__image {
    width: 100%;
    max-width: 40rem;
  }

  /* Buttons responsive */
  .d-flex.align-items-center.justify-content-center {
    flex-direction: column;
    gap: 1rem !important;
  }

  .df-btn--primary,
  .df-btn--outline-primary {
    width: 100%;
    max-width: 280px;
    padding: 0.875rem 1rem;
    font-size: 1rem;
  }

  .df-text-button {
    font-size: 0.9rem;
  }

  /* Services section responsive */
  .df-services {
    padding: 4rem 1rem;
  }

  .df-services-title {
    font-size: 2.5rem;
  }

  .df-services-subtitle {
    font-size: 0.9rem;
  }

  .df-platforms-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-top: 3rem;
  }

  .df-platform-card {
    padding: 1.5rem;
  }

  .df-platform-header {
    margin-bottom: 1.25rem;
  }

  .df-platform-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .df-platform-icon svg {
    width: 1.25rem;
    height: 1.25rem;
  }

  .df-platform-info h3 {
    font-size: 1.125rem;
  }

  .df-service-item {
    padding: 0.625rem 0;
  }

  .df-service-icon {
    width: 1rem;
    height: 1rem;
  }

  .df-service-name {
    font-size: 0.8rem;
  }

  /* About section responsive */
  .df-about {
    padding: 4rem 1rem;
  }

  .df-about-title {
    font-size: 2.5rem;
  }

  .df-about-subtitle {
    font-size: 0.9rem;
    margin-bottom: 2rem;
  }

  .df-features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .df-feature-item {
    padding: 1rem;
  }

  .df-feature-icon {
    width: 3rem;
    height: 3rem;
  }

  .df-feature-icon svg {
    width: 1.5rem;
    height: 1.5rem;
  }

  .df-feature-title {
    font-size: 1.125rem;
  }

  .df-feature-desc {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  /* Extra small screens */
  .df-intro {
    padding: 12rem 0.5rem;
  }

  .df-intro-head {
    font-size: 2rem;
  }

  .df-intro-sub {
    font-size: 0.85rem;
  }

  .df-nav-list {
    gap: 0.75rem;
  }

  .df-nav-item {
    font-size: 0.8rem;
  }

  .df-logo__text {
    font-size: 1.1rem;
  }
  .mt-8{
    margin-top: 4rem;
  }

  /* Services section for extra small screens */
  .df-services {
    padding: 3rem 0.5rem;
  }

  .df-services-title {
    font-size: 2rem;
  }

  .df-services-subtitle {
    font-size: 0.85rem;
    padding: 0 1rem;
  }

  .df-platform-card {
    padding: 1.25rem;
  }

  .df-platform-icon {
    width: 2.25rem;
    height: 2.25rem;
  }

  .df-platform-icon svg {
    width: 1.125rem;
    height: 1.125rem;
  }

  .df-platform-info h3 {
    font-size: 1rem;
  }

  .df-platform-info p {
    font-size: 0.8rem;
  }

  /* About section for extra small screens */
  .df-about {
    padding: 3rem 0.5rem;
  }

  .df-about-title {
    font-size: 2rem;
  }

  .df-about-subtitle {
    font-size: 0.85rem;
    padding: 0 1rem;
  }

  .df-feature-item {
    padding: 0.75rem;
  }

  .df-feature-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .df-feature-icon svg {
    width: 1.25rem;
    height: 1.25rem;
  }

  .df-feature-title {
    font-size: 1rem;
  }

  .df-feature-desc {
    font-size: 0.75rem;
  }
}

/* Tablet responsive */
@media (min-width: 769px) and (max-width: 1024px) {
  .df-nav-list {
    gap: 2rem;
  }

  .df-intro {
    padding: 8rem 1.5rem;
  }

  .df-intro-head {
    font-size: 3rem;
  }

  .df-intro-main__image {
    width: 60rem;
  }
}

/* Touch improvements for mobile */
@media (max-width: 768px) {
  /* Improve touch targets */
  .df-nav-item {
    padding: 0.75rem 1rem;
    display: block;
    border-radius: 0.5rem;
    transition: background-color 0.2s;
  }

  .df-nav-item:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
  }

  /* Better spacing for mobile */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Ensure buttons are easily tappable */
  .df-btn--primary,
  .df-btn--outline-primary {
    min-height: 44px;
    touch-action: manipulation;
  }
}

/* Authentication Container Styles */
.df-auth-container {
  background: linear-gradient(135deg, rgba(var(--bs-primary-rgb), 0.05) 0%, rgba(var(--bs-primary-rgb), 0.02) 100%);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(var(--bs-primary-rgb), 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.df-auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--bs-primary), #4a92e6, var(--bs-primary));
  border-radius: 1.5rem 1.5rem 0 0;
}

/* Tab Navigation */
.df-auth-tabs {
  display: flex;
  background-color: rgba(var(--bs-body-color-rgb), 0.05);
  border-radius: 1rem;
  padding: 0.5rem;
  margin-bottom: 2rem;
  position: relative;
}

.df-auth-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  color: var(--bs-body-color);
  font-weight: 500;
  font-size: 0.9rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.df-auth-tab:hover {
  color: var(--bs-primary);
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.df-auth-tab.active {
  background: linear-gradient(135deg, var(--bs-primary), #4a92e6);
  color: white;
  box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.3);
  transform: translateY(-1px);
}

.df-tab-icon {
  width: 1.25rem;
  height: 1.25rem;
  transition: transform 0.3s ease;
}

.df-auth-tab.active .df-tab-icon {
  transform: scale(1.1);
}


@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Footer */
.df-footer {
    position: relative;
    padding: 4rem 2rem 2rem;
    isolation: isolate;
}

.df-footer::before {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 2.5rem;
    z-index: -10;
    display: flex;
    justify-content: center;
    overflow: hidden;
    filter: blur(64px);
    aspect-ratio: 1108 / 632;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(to right, #1559b4, #2563eb);
    opacity: .1;
    clip-path: polygon(73.6% 51.7%, 91.7% 11.8%, 100% 46.4%, 97.4% 82.2%, 92.5% 84.9%, 75.7% 64%, 55.3% 47.5%, 46.5% 49.4%, 45% 62.9%, 50.3% 87.2%, 21.3% 64.1%, 0.1% 100%, 5.4% 51.1%, 21.4% 63.9%, 58.9% 0.2%, 73.6% 51.7%);
}

.df-footer-logo {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--bs-body-color);
    margin-bottom: 1rem;
}

.df-footer-desc {
    color: var(--bs-body-color);
    opacity: 0.8;
    font-size: 0.875rem;
    line-height: 1.6;
}

.df-footer-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--bs-body-color);
    margin-bottom: 1.5rem;
}

.df-footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.df-footer-links li {
    margin-bottom: 0.75rem;
}

.df-footer-links a {
    color: var(--bs-body-color);
    text-decoration: none;
    font-size: 0.875rem;
    opacity: 0.8;
    transition: all 0.2s;
}

.df-footer-links a:hover {
    color: var(--bs-primary);
    opacity: 1;
}

.df-social-links {
    display: flex;
    gap: 1rem;
}

.df-social-link {
    width: 2.5rem;
    height: 2.5rem;
    background-color: rgba(var(--bs-body-color-rgb), 0.1);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bs-body-color);
    text-decoration: none;
    transition: all 0.2s;
}

.df-social-link:hover {
    background-color: var(--bs-primary);
    color: white;
}

.df-social-link svg {
    width: 1.25rem;
    height: 1.25rem;
}

.df-footer-divider {
    border-color: rgba(var(--bs-body-color-rgb), 0.1);
    margin: 2rem 0 1.5rem;
}

.df-footer-copyright,
.df-footer-made {
    font-size: 0.875rem;
    color: var(--bs-body-color);
    opacity: 0.8;
    margin: 0;
}

/* Go to Top Button */
.df-go-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3rem;
    height: 3rem;
    background-color: var(--bs-primary);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.df-go-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.df-go-to-top:hover {
    background-color: #1d4ed8;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.df-go-to-top svg {
    width: 1.25rem;
    height: 1.25rem;
}

[data-bs-theme="dark"] .df-go-to-top {
    background-color: var(--bs-white);
    color: var(--bs-primary);
}

[data-bs-theme="dark"] .df-go-to-top:hover {
    background-color: #f8f9fa;
}

/* Mobile responsive for auth */
@media (max-width: 768px) {
  .df-auth-container {
    padding: 1.5rem;
    border-radius: 1rem;
  }

  .df-auth-tabs {
    padding: 0.25rem;
    margin-bottom: 1.5rem;
  }

  .df-auth-tab {
    padding: 0.75rem 1rem;
    font-size: 0.85rem;
  }

  .df-tab-icon {
    width: 1rem;
    height: 1rem;
  }

  .df-form-row {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .df-form-input {
    padding: 0.875rem 1rem;
    font-size: 0.875rem;
  }

  .df-form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .df-social-buttons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .df-social-btn {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }

  .df-social-icon {
    width: 1rem;
    height: 1rem;
  }

  .df-auth-btn {
    padding: 0.875rem 1.25rem;
    font-size: 0.875rem;
  }
}